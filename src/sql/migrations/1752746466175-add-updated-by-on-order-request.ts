import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddUpdatedByOnOrderRequest1752746466175 implements MigrationInterface {
  name = 'AddUpdatedByOnOrderRequest1752746466175'

  public async up (queryRunner: QueryRunner): Promise<void> {
    // Add the column as nullable first
    await queryRunner.query(`ALTER TABLE "order_request" ADD "updated_by_user_uuid" uuid`)

    // Copy createdByUserUuid to updatedByUserUuid for existing records
    await queryRunner.query(`UPDATE "order_request" SET "updated_by_user_uuid" = "created_by_user_uuid" WHERE "updated_by_user_uuid" IS NULL`)

    // Make the column NOT NULL
    await queryRunner.query(`ALTER TABLE "order_request" ALTER COLUMN "updated_by_user_uuid" SET NOT NULL`)

    // Add the foreign key constraint
    await queryRunner.query(`ALTER TABLE "order_request" ADD CONSTRAINT "FK_776ab91ef06888d54dc675643ef" FOREIGN KEY ("updated_by_user_uuid") REFERENCES "user"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order_request" DROP CONSTRAINT "FK_776ab91ef06888d54dc675643ef"`)
    await queryRunner.query(`ALTER TABLE "order_request" DROP COLUMN "updated_by_user_uuid"`)
  }
}
