import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { OrderRequestStatus } from '../../enums/order-request-status.js'
import { ClientType } from '../../../client/client-type.js'

class OrderRequestContractTypeResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: String })
  abbreviation: string

  constructor (uuid: string, name: string, abbreviation: string) {
    this.uuid = uuid
    this.name = name
    this.abbreviation = abbreviation
  }
}

class OrderRequestContractResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: OrderRequestContractTypeResponse })
  contractType: OrderRequestContractTypeResponse

  constructor (uuid: string, contractType: OrderRequestContractTypeResponse) {
    this.uuid = uuid
    this.contractType = contractType
  }
}

class OrderRequestCareUserResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  constructor (uuid: string, name: string) {
    this.uuid = uuid
    this.name = name
  }
}

class OrderRequestClientResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ enum: ClientType })
  type: ClientType

  constructor (uuid: string, name: string, type: ClientType) {
    this.uuid = uuid
    this.name = name
    this.type = type
  }
}

class OrderRequestUserResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, nullable: true })
  firstName: string | null

  @ApiProperty({ type: String, nullable: true })
  lastName: string | null

  constructor (uuid: string, firstName: string | null, lastName: string | null) {
    this.uuid = uuid
    this.firstName = firstName
    this.lastName = lastName
  }
}

export class OrderRequestResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, format: 'uuid' })
  clientUuid: string

  @ApiProperty({ enum: ClientType })
  clientType: ClientType

  @ApiProperty({ enum: OrderRequestStatus })
  status: OrderRequestStatus

  @ApiProperty({ type: Boolean })
  isRecurring: boolean

  @ApiProperty({ type: String, format: 'date', nullable: true })
  startDate: string | null

  @ApiProperty({ type: Number, nullable: true })
  iterationIntervalWeeks: number | null

  @ApiProperty({ type: OrderRequestClientResponse })
  client: OrderRequestClientResponse

  @ApiProperty({ type: OrderRequestContractResponse })
  contract: OrderRequestContractResponse

  @ApiProperty({ type: OrderRequestCareUserResponse })
  careUser: OrderRequestCareUserResponse

  @ApiProperty({ type: OrderRequestUserResponse })
  createdByUser: OrderRequestUserResponse

  @ApiProperty({ type: OrderRequestUserResponse })
  updatedByUser: OrderRequestUserResponse

  constructor (orderRequest: OrderRequest) {
    assert(orderRequest.client !== undefined, 'client is required')
    assert(orderRequest.contract !== undefined, 'contract is required')
    assert(orderRequest.contract.contractType !== undefined, 'contract.contractType is required')
    assert(orderRequest.careUser !== undefined, 'careUser is required')
    assert(orderRequest.createdByUser !== undefined, 'createdByUser is required')
    assert(orderRequest.updatedByUser !== undefined, 'updatedByUser is required')

    this.uuid = orderRequest.uuid
    this.createdAt = orderRequest.createdAt.toISOString()
    this.updatedAt = orderRequest.updatedAt.toISOString()
    this.clientUuid = orderRequest.clientUuid
    this.clientType = orderRequest.clientType
    this.status = orderRequest.status
    this.isRecurring = orderRequest.isRecurring
    this.startDate = orderRequest.startDate?.toString() ?? null
    this.iterationIntervalWeeks = orderRequest.iterationIntervalWeeks

    this.client = new OrderRequestClientResponse(
      orderRequest.client.uuid,
      orderRequest.client.name,
      orderRequest.client.type
    )

    this.contract = new OrderRequestContractResponse(
      orderRequest.contract.uuid,
      new OrderRequestContractTypeResponse(
        orderRequest.contract.contractType.uuid,
        orderRequest.contract.contractType.name,
        orderRequest.contract.contractType.abbreviation
      )
    )

    this.careUser = new OrderRequestCareUserResponse(
      orderRequest.careUser.uuid,
      orderRequest.careUser.name
    )

    this.createdByUser = new OrderRequestUserResponse(
      orderRequest.createdByUser.uuid,
      orderRequest.createdByUser.firstName,
      orderRequest.createdByUser.lastName
    )

    this.updatedByUser = new OrderRequestUserResponse(
      orderRequest.updatedByUser.uuid,
      orderRequest.updatedByUser.firstName,
      orderRequest.updatedByUser.lastName
    )
  }
}

export class ViewOrderRequestIndexResponse extends PaginatedOffsetResponse<OrderRequestResponse> {
  @ApiProperty({ type: OrderRequestResponse, isArray: true })
  declare items: OrderRequestResponse[]

  constructor (items: OrderRequest[], total: number, limit: number, offset: number) {
    const result = items.map(orderRequest => new OrderRequestResponse(orderRequest))

    super(result, total, limit, offset)
  }
}
