import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { SortDirection } from '@wisemen/pagination'
import { WiseDate } from '@wisemen/wise-date'
import { stringify } from 'qs'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { OrderRequest } from '../../../entities/order-request.entity.js'
import { OrderRequestEntityBuilder } from '../../../entities/order-request.entity.builder.js'
import { ContractEntityBuilder } from '../../../../contract/entities/contract.entity.builder.js'
import { ContractTypeEntityBuilder } from '../../../../contract-type/builders/contract-type.entity.builder.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { Contract } from '../../../../contract/entities/contract.entity.js'
import { ContractType } from '../../../../contract-type/entities/contract-type.entity.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { ViewOrderRequestIndexQueryBuilder } from '../view-order-request-index.query-builder.js'
import { OrderRequestStatus } from '../../../enums/order-request-status.js'
import { ViewOrderRequestIndexSortQueryKey } from '../view-order-request-index.sort-query.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { MaxTimeInVehicleFormula } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { ClientType } from '../../../../client/client-type.js'

describe('View order request index e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser

  let orderRequest1: OrderRequest
  let orderRequest2: OrderRequest
  let contract: Contract
  let contractType: ContractType
  let careUser: CareUser
  let createdByUser: User

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()

    const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder()
      .build()
    await setup.dataSource.manager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)

    const pricingFormula = new PricingFormulaEntityBuilder().build()
    await setup.dataSource.manager.insert(PricingFormula, pricingFormula)

    contractType = new ContractTypeEntityBuilder()
      .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
      .withPricingFormulaUuid(pricingFormula.uuid)
      .build()
    await setup.dataSource.manager.insert(ContractType, contractType)

    contract = new ContractEntityBuilder()
      .withContractTypeUuid(contractType.uuid)
      .build()
    await setup.dataSource.manager.insert(Contract, contract)

    careUser = new CareUserEntityBuilder().build()
    await setup.dataSource.manager.insert(CareUser, careUser)

    createdByUser = new UserEntityBuilder().build()
    await setup.dataSource.manager.insert(User, createdByUser)

    orderRequest1 = new OrderRequestEntityBuilder()
      .withCreatedAt(WiseDate.today())
      .withContractUuid(contract.uuid)
      .withCareUserUuid(careUser.uuid)
      .withCreatedByUserUuid(createdByUser.uuid)
      .withUpdatedByUserUuid(createdByUser.uuid)
      .withClientUuid(careUser.uuid)
      .withClientType(ClientType.CARE_USER)
      .withStatus(OrderRequestStatus.DRAFT)
      .build()

    orderRequest2 = new OrderRequestEntityBuilder()
      .withCreatedAt(WiseDate.yesterday())
      .withContractUuid(contract.uuid)
      .withCareUserUuid(careUser.uuid)
      .withCreatedByUserUuid(createdByUser.uuid)
      .withUpdatedByUserUuid(createdByUser.uuid)
      .withClientUuid(careUser.uuid)
      .withClientType(ClientType.CARE_USER)
      .withStatus(OrderRequestStatus.REQUESTED)
      .build()

    await setup.dataSource.manager.insert(OrderRequest, [orderRequest1, orderRequest2])
  })

  after(async () => {
    await setup.teardown()
  })

  it('returns order requests in a paginated format', async () => {
    const query = new ViewOrderRequestIndexQueryBuilder()
      .withPagination({
        limit: 10,
        offset: 0
      })
      .build()

    const response = await request(setup.httpServer)
      .get('/order-requests')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(2)
    expect(response.body.items[0].uuid).toBe(orderRequest1.uuid)
    expect(response.body.items[0].status).toBe(OrderRequestStatus.DRAFT)
    expect(response.body.items[0].contract.uuid).toBe(contract.uuid)
    expect(response.body.items[0].careUser.uuid).toBe(careUser.uuid)
    expect(response.body.items[0].createdByUser.uuid).toBe(createdByUser.uuid)
    expect(response.body.items[1].uuid).toBe(orderRequest2.uuid)
    expect(response.body.items[1].status).toBe(OrderRequestStatus.REQUESTED)
    expect(response.body.items[1].contract.uuid).toBe(contract.uuid)
    expect(response.body.items[1].careUser.uuid).toBe(careUser.uuid)
    expect(response.body.items[1].createdByUser.uuid).toBe(createdByUser.uuid)
  })

  it('orders order requests by status ascending', async () => {
    const query = new ViewOrderRequestIndexQueryBuilder()
      .withPagination({
        limit: 10,
        offset: 0
      })
      .withSort(ViewOrderRequestIndexSortQueryKey.STATUS, SortDirection.DESC)
      .build()

    const response = await request(setup.httpServer)
      .get('/order-requests')
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(200)
    expect(response.body.items).toHaveLength(2)
    expect(response.body.items[0].uuid).toBe(orderRequest2.uuid)
    expect(response.body.items[1].uuid).toBe(orderRequest1.uuid)
  })
})
