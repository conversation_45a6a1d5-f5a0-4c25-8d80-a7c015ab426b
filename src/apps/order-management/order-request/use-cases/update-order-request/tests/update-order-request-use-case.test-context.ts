import { randomUUID } from 'crypto'
import { <PERSON>tityManager } from 'typeorm'
import { OrganizationEntityBuilder } from '../../../../organization/builders/organization.entity.builder.js'
import { ContractTypeEntityBuilder } from '../../../../contract-type/builders/contract-type.entity.builder.js'
import { Organization } from '../../../../organization/entities/organization.entity.js'
import { ContractType } from '../../../../contract-type/entities/contract-type.entity.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { MaxTimeInVehicleFormula } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { OrderRequestEntityBuilder } from '../../../entities/order-request.entity.builder.js'
import { ClientType } from '../../../../client/client-type.js'
import { OrderRequest } from '../../../entities/order-request.entity.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { generateUserUuid } from '../../../../../../app/users/entities/user.uuid.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { Contract } from '../../../../contract/entities/contract.entity.js'
import { ContractEntityBuilder } from '../../../../contract/entities/contract.entity.builder.js'
import { ClientId } from '../../../../client/client-id.js'

export interface OrderRequestTestContext {
  careUserContract: Contract
  organizationContract: Contract
  organization: Organization
  careUser: CareUser
  orderRequest: OrderRequest
  createdByUser: User
}

export async function setupOrderRequestTestContext (
  entityManager: EntityManager
): Promise<OrderRequestTestContext> {
  const user = new UserEntityBuilder()
    .withUuid(generateUserUuid())
    .withEmail(randomUUID() + '@mail.com')
    .build()
  const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder().build()
  const pricingFormula = new PricingFormulaEntityBuilder().build()
  const contractType = new ContractTypeEntityBuilder()
    .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
    .withPricingFormulaUuid(pricingFormula.uuid)
    .build()
  const organization = new OrganizationEntityBuilder()
    .build()

  const careUser = new CareUserEntityBuilder()
    .build()

  const careUserContract = new ContractEntityBuilder()
    .withContractTypeUuid(contractType.uuid)
    .withClientId(new ClientId(careUser.uuid, ClientType.CARE_USER))
    .build()

  const organizationContract = new ContractEntityBuilder()
    .withContractTypeUuid(contractType.uuid)
    .withClientId(new ClientId(organization.uuid, ClientType.ORGANIZATION))
    .build()

  const orderRequest = new OrderRequestEntityBuilder()
    .withContractUuid(careUserContract.uuid)
    .withClientUuid(organization.uuid)
    .withClientType(ClientType.ORGANIZATION)
    .withCareUserUuid(careUser.uuid)
    .withCreatedByUserUuid(user.uuid)
    .withUpdatedByUserUuid(user.uuid)
    .build()

  await entityManager.insert(User, user)
  await entityManager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)
  await entityManager.insert(PricingFormula, pricingFormula)
  await entityManager.insert(ContractType, contractType)
  await entityManager.insert(Organization, organization)
  await entityManager.insert(CareUser, careUser)
  await entityManager.insert(Contract, [organizationContract, careUserContract])
  await entityManager.insert(OrderRequest, orderRequest)

  return {
    careUserContract,
    organizationContract,
    organization,
    careUser,
    orderRequest,
    createdByUser: user
  }
}
