import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { OrderRequestStatus } from '../../enums/order-request-status.js'
import { ClientType } from '../../../client/client-type.js'
import { RequestedTransportOrder } from '../../../requested-transport-order/requested-transport-order.entity.js'
import { DayOfWeekApiProperty, DayOfWeek } from '../../../../pricing/pricing-parameters/types/flexibility-factor/day-of-week.enum.js'
import { TargetActionApiProperty } from '../../../accepted-transport-order/enums/target-action.enum.js'
import { SeatsDemandResponse } from '../../../seats-demand/seats-demand.response.js'
import { AddressResponse } from '../../../../../utils/address/address-response.js'
import { AssistanceTypeApiProperty } from '../../../requested-transport-order/assistance-type.enum.js'

class OrderRequestDetailContractTypeResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: String })
  abbreviation: string

  constructor (uuid: string, name: string, abbreviation: string) {
    this.uuid = uuid
    this.name = name
    this.abbreviation = abbreviation
  }
}

class OrderRequestDetailContractResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: OrderRequestDetailContractTypeResponse })
  contractType: OrderRequestDetailContractTypeResponse

  constructor (uuid: string, contractType: OrderRequestDetailContractTypeResponse) {
    this.uuid = uuid
    this.contractType = contractType
  }
}

class OrderRequestDetailCareUserResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  constructor (uuid: string, name: string) {
    this.uuid = uuid
    this.name = name
  }
}

class OrderRequestDetailUserResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, nullable: true })
  firstName: string | null

  @ApiProperty({ type: String, nullable: true })
  lastName: string | null

  constructor (uuid: string, firstName: string | null, lastName: string | null) {
    this.uuid = uuid
    this.firstName = firstName
    this.lastName = lastName
  }
}

class OrderRequestDetailClientResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ enum: ClientType })
  type: ClientType

  constructor (uuid: string, name: string, type: ClientType) {
    this.uuid = uuid
    this.name = name
    this.type = type
  }
}

class OrderRequestRequestedTransportOrderResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, format: 'date' })
  date: string | null

  @DayOfWeekApiProperty({ isArray: true, nullable: true })
  daysOfWeek: DayOfWeek[] | null

  @ApiProperty({ type: Boolean, nullable: true })
  planOnHolidays: boolean | null

  @TargetActionApiProperty()
  targetAction: string

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  targetTime: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  arrivalWindowFrom: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  arrivalWindowUntil: string | null

  @ApiProperty({ type: SeatsDemandResponse })
  seatsDemand: SeatsDemandResponse

  @ApiProperty({ type: AddressResponse, nullable: true })
  pickupAddress: AddressResponse | null

  @AssistanceTypeApiProperty()
  pickupAssistanceType: string

  @ApiProperty({ type: Number, nullable: true })
  pickupStopDuration: number | null

  @ApiProperty({ type: Number, nullable: true })
  pickupActionDuration: number | null

  @ApiProperty({ type: String, nullable: true })
  pickupRemarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  pickupRemarksForPlanner: string | null

  @ApiProperty({ type: AddressResponse, nullable: true })
  dropOffAddress: AddressResponse | null

  @AssistanceTypeApiProperty()
  dropOffAssistanceType: string

  @ApiProperty({ type: Number, nullable: true })
  dropOffStopDuration: number | null

  @ApiProperty({ type: Number, nullable: true })
  dropOffActionDuration: number | null

  @ApiProperty({ type: String, nullable: true })
  dropOffRemarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  dropOffRemarksForPlanner: string | null

  constructor (requestedTransportOrder: RequestedTransportOrder) {
    this.uuid = requestedTransportOrder.uuid
    this.createdAt = requestedTransportOrder.createdAt.toISOString()
    this.updatedAt = requestedTransportOrder.updatedAt.toISOString()
    this.date = requestedTransportOrder.date
    this.daysOfWeek = requestedTransportOrder.daysOfWeek
    this.planOnHolidays = requestedTransportOrder.planOnHolidays
    this.targetAction = requestedTransportOrder.targetAction
    this.targetTime = requestedTransportOrder.targetTime?.toISOString() ?? null
    this.arrivalWindowFrom = requestedTransportOrder.arrivalWindowFrom?.toISOString() ?? null
    this.arrivalWindowUntil = requestedTransportOrder.arrivalWindowUntil?.toISOString() ?? null
    this.seatsDemand = new SeatsDemandResponse(requestedTransportOrder.seatsDemand)
    this.pickupAddress = requestedTransportOrder.pickupAddress
      ? new AddressResponse(requestedTransportOrder.pickupAddress)
      : null
    this.pickupAssistanceType = requestedTransportOrder.pickupAssistanceType
    this.pickupStopDuration = requestedTransportOrder.pickupStopDuration
    this.pickupActionDuration = requestedTransportOrder.pickupActionDuration
    this.pickupRemarksForDriver = requestedTransportOrder.pickupRemarksForDriver
    this.pickupRemarksForPlanner = requestedTransportOrder.pickupRemarksForPlanner
    this.dropOffAddress = requestedTransportOrder.dropOffAddress
      ? new AddressResponse(requestedTransportOrder.dropOffAddress)
      : null
    this.dropOffAssistanceType = requestedTransportOrder.dropOffAssistanceType
    this.dropOffStopDuration = requestedTransportOrder.dropOffStopDuration
    this.dropOffActionDuration = requestedTransportOrder.dropOffActionDuration
    this.dropOffRemarksForDriver = requestedTransportOrder.dropOffRemarksForDriver
    this.dropOffRemarksForPlanner = requestedTransportOrder.dropOffRemarksForPlanner
  }
}

export class ViewOrderRequestDetailResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, format: 'uuid' })
  clientUuid: string

  @ApiProperty({ enum: ClientType })
  clientType: ClientType

  @ApiProperty({ enum: OrderRequestStatus })
  status: OrderRequestStatus

  @ApiProperty({ type: Boolean })
  isRecurring: boolean

  @ApiProperty({ type: String, format: 'date', nullable: true })
  startDate: string | null

  @ApiProperty({ type: Number, nullable: true })
  iterationIntervalWeeks: number | null

  @ApiProperty({ type: String, nullable: true })
  remarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  remarksForPlanner: string | null

  @ApiProperty({ type: OrderRequestDetailClientResponse })
  client: OrderRequestDetailClientResponse

  @ApiProperty({ type: OrderRequestDetailContractResponse })
  contract: OrderRequestDetailContractResponse

  @ApiProperty({ type: OrderRequestDetailCareUserResponse })
  careUser: OrderRequestDetailCareUserResponse

  @ApiProperty({ type: OrderRequestDetailUserResponse })
  createdByUser: OrderRequestDetailUserResponse

  @ApiProperty({ type: OrderRequestDetailUserResponse })
  updatedByUser: OrderRequestDetailUserResponse

  @ApiProperty({ type: OrderRequestRequestedTransportOrderResponse, isArray: true })
  requestedTransportOrders: OrderRequestRequestedTransportOrderResponse[]

  constructor (orderRequest: OrderRequest) {
    assert(orderRequest.client !== undefined, 'client is required')
    assert(orderRequest.contract !== undefined, 'contract is required')
    assert(orderRequest.contract.contractType !== undefined, 'contract.contractType is required')
    assert(orderRequest.careUser !== undefined, 'careUser is required')
    assert(orderRequest.createdByUser !== undefined, 'createdByUser is required')
    assert(orderRequest.updatedByUser !== undefined, 'updatedByUser is required')
    assert(orderRequest.requestedTransportOrders !== undefined, 'requestedTransportOrders is required')

    this.uuid = orderRequest.uuid
    this.createdAt = orderRequest.createdAt.toISOString()
    this.updatedAt = orderRequest.updatedAt.toISOString()
    this.clientUuid = orderRequest.clientUuid
    this.clientType = orderRequest.clientType
    this.status = orderRequest.status
    this.isRecurring = orderRequest.isRecurring
    this.startDate = orderRequest.startDate?.toString() ?? null
    this.iterationIntervalWeeks = orderRequest.iterationIntervalWeeks
    this.remarksForDriver = orderRequest.remarksForDriver
    this.remarksForPlanner = orderRequest.remarksForPlanner

    this.client = new OrderRequestDetailClientResponse(
      orderRequest.client.uuid,
      orderRequest.client.name,
      orderRequest.client.type
    )

    this.contract = new OrderRequestDetailContractResponse(
      orderRequest.contract.uuid,
      new OrderRequestDetailContractTypeResponse(
        orderRequest.contract.contractType.uuid,
        orderRequest.contract.contractType.name,
        orderRequest.contract.contractType.abbreviation
      )
    )

    this.careUser = new OrderRequestDetailCareUserResponse(
      orderRequest.careUser.uuid,
      orderRequest.careUser.name
    )

    this.createdByUser = new OrderRequestDetailUserResponse(
      orderRequest.createdByUser.uuid,
      orderRequest.createdByUser.firstName,
      orderRequest.createdByUser.lastName
    )

    this.updatedByUser = new OrderRequestDetailUserResponse(
      orderRequest.updatedByUser.uuid,
      orderRequest.updatedByUser.firstName,
      orderRequest.updatedByUser.lastName
    )

    this.requestedTransportOrders = orderRequest.requestedTransportOrders.map(
      requestedTransportOrder =>
        new OrderRequestRequestedTransportOrderResponse(requestedTransportOrder)
    )
  }
}
