import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { OrderRequest } from '../../entities/order-request.entity.js'
import { ClientId } from '../../../client/client-id.js'
import { ClientRepository } from '../../../client/client-repository.js'
import { ViewOrderRequestDetailResponse } from './view-order-request-detail.response.js'

@Injectable()
export class ViewOrderRequestDetailUseCase {
  constructor (
    @InjectRepository(OrderRequest)
    private readonly orderRequestRepository: Repository<OrderRequest>,
    private readonly clientRepo: ClientRepository
  ) {}

  async execute (orderRequestUuid: string): Promise<ViewOrderRequestDetailResponse> {
    const orderRequest = await this.orderRequestRepository.findOneOrFail({
      where: { uuid: orderRequestUuid },
      relations: {
        contract: {
          contractType: true
        },
        careUser: true,
        createdByUser: true,
        updatedByUser: true,
        requestedTransportOrders: true
      }
    })

    const client = await this.clientRepo.findByIdOrFail(
      new ClientId(orderRequest.clientUuid, orderRequest.clientType)
    )

    orderRequest.client = client

    return new ViewOrderRequestDetailResponse(orderRequest)
  }
}
