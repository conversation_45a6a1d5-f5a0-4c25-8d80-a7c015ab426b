import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { WiseDate } from '@wisemen/wise-date'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { OrderRequest } from '../../../entities/order-request.entity.js'
import { OrderRequestEntityBuilder } from '../../../entities/order-request.entity.builder.js'
import { ContractEntityBuilder } from '../../../../contract/entities/contract.entity.builder.js'
import { ContractTypeEntityBuilder } from '../../../../contract-type/builders/contract-type.entity.builder.js'
import { CareUserEntityBuilder } from '../../../../care-user/builders/care-user.entity.builder.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { Contract } from '../../../../contract/entities/contract.entity.js'
import { ContractType } from '../../../../contract-type/entities/contract-type.entity.js'
import { CareUser } from '../../../../care-user/entities/care-user.entity.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { OrderRequestStatus } from '../../../enums/order-request-status.js'
import { ClientType } from '../../../../client/client-type.js'
import { PricingFormula } from '../../../../../pricing/pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../../pricing/pricing-formula/pricing-formula.entity.builder.js'
import { MaxTimeInVehicleFormulaEntityBuilder } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.builder.js'
import { MaxTimeInVehicleFormula } from '../../../../max-time-in-vehicle-formula/max-time-in-vehicle-formula.entity.js'
import { RequestedTransportOrderEntityBuilder } from '../../../../requested-transport-order/requested-transport-order.entity.builder.js'
import { RequestedTransportOrder } from '../../../../requested-transport-order/requested-transport-order.entity.js'

describe('View order request detail e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser

  let orderRequest: OrderRequest
  let contract: Contract
  let contractType: ContractType
  let careUser: CareUser
  let createdByUser: User

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()

    const timeInVehicleFormula = new MaxTimeInVehicleFormulaEntityBuilder()
      .build()
    await setup.dataSource.manager.insert(MaxTimeInVehicleFormula, timeInVehicleFormula)

    const pricingFormula = new PricingFormulaEntityBuilder().build()
    await setup.dataSource.manager.insert(PricingFormula, pricingFormula)

    contractType = new ContractTypeEntityBuilder()
      .withMaxTimeInVehicleFormulaUuid(timeInVehicleFormula.uuid)
      .withPricingFormulaUuid(pricingFormula.uuid)
      .build()
    await setup.dataSource.manager.insert(ContractType, contractType)

    contract = new ContractEntityBuilder()
      .withContractTypeUuid(contractType.uuid)
      .build()
    await setup.dataSource.manager.insert(Contract, contract)

    careUser = new CareUserEntityBuilder().build()
    await setup.dataSource.manager.insert(CareUser, careUser)

    createdByUser = new UserEntityBuilder().build()
    await setup.dataSource.manager.insert(User, createdByUser)

    orderRequest = new OrderRequestEntityBuilder()
      .withCreatedAt(WiseDate.today())
      .withContractUuid(contract.uuid)
      .withCareUserUuid(careUser.uuid)
      .withCreatedByUserUuid(createdByUser.uuid)
      .withUpdatedByUserUuid(createdByUser.uuid)
      .withClientUuid(careUser.uuid)
      .withClientType(ClientType.CARE_USER)
      .withStatus(OrderRequestStatus.DRAFT)
      .build()
    await setup.dataSource.manager.insert(OrderRequest, orderRequest)

    const requestedTransportOrder1 = new RequestedTransportOrderEntityBuilder()
      .withOrderRequestUuid(orderRequest.uuid)
      .build()

    const requestedTransportOrder2 = new RequestedTransportOrderEntityBuilder()
      .withOrderRequestUuid(orderRequest.uuid)
      .build()

    await setup.dataSource.manager.insert(
      RequestedTransportOrder,
      [requestedTransportOrder1, requestedTransportOrder2]
    )
  })

  after(async () => {
    await setup.teardown()
  })

  it('returns order request details', async () => {
    const response = await request(setup.httpServer)
      .get(`/order-requests/${orderRequest.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(200)
    expect(response.body.uuid).toBe(orderRequest.uuid)
    expect(response.body.status).toBe(OrderRequestStatus.DRAFT)
    expect(response.body.contract.uuid).toBe(contract.uuid)
    expect(response.body.contract.contractType.uuid).toBe(contractType.uuid)
    expect(response.body.careUser.uuid).toBe(careUser.uuid)
    expect(response.body.createdByUser.uuid).toBe(createdByUser.uuid)
    expect(response.body.clientUuid).toBe(orderRequest.clientUuid)
    expect(response.body.clientType).toBe(orderRequest.clientType)
    expect(response.body.isRecurring).toBe(orderRequest.isRecurring)
    expect(response.body.remarksForDriver).toBe(orderRequest.remarksForDriver)
    expect(response.body.remarksForPlanner).toBe(orderRequest.remarksForPlanner)
  })
})
