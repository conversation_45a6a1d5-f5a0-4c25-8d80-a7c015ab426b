import { ApiPropertyOptions, ApiProperty } from '@nestjs/swagger'
import { Column } from 'typeorm'

export enum AssistanceType {
  NONE = 'none',
  DROP_OFF = 'drop_off',
  PICKUP = 'pickup',
  CHECK_IN = 'check_in'
}

export function AssistanceTypeApiProperty (options?: ApiPropertyOptions): PropertyDecorator {
  return ApiProperty({
    ...options,
    enum: AssistanceType,
    enumName: 'AssistanceType'
  })
}

export function AssistanceTypeColumn (
  options?: { default: AssistanceType }
): PropertyDecorator {
  return Column({
    type: 'enum',
    enum: AssistanceType,
    enumName: 'assistance_type',
    ...options
  })
}
