import { Column, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { OrderRequest } from '../order-request/entities/order-request.entity.js'
import { AddressColumn } from '../../../utils/address/address-column.js'
import { Address } from '../../../utils/address/address.js'
import { TargetAction } from '../accepted-transport-order/enums/target-action.enum.js'
import { SeatsDemand } from '../seats-demand/seats-demand.js'
import { DayOfWeek, DayOfWeekColumn } from '../../pricing/pricing-parameters/types/flexibility-factor/day-of-week.enum.js'
import { AssistanceTypeColumn, AssistanceType } from './assistance-type.enum.js'
import { RequestedTransportOrderUuid } from './requested-transport-order.uuid.js'

@Entity()
export class RequestedTransportOrder {
  @PrimaryGeneratedColumn('uuid')
  uuid: RequestedTransportOrderUuid

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @Index()
  @Column({ type: 'date', nullable: true })
  date: string | null

  @DayOfWeekColumn({ array: true, nullable: true })
  daysOfWeek: DayOfWeek[] | null

  @Column({ type: 'boolean', nullable: true })
  planOnHolidays: boolean | null

  @Column({ type: 'enum', enum: TargetAction })
  targetAction: TargetAction

  @Column({ type: 'timestamptz', nullable: true })
  targetTime: Date | null

  @Column({ type: 'timestamptz', nullable: true })
  arrivalWindowFrom: Date | null

  @Column({ type: 'timestamptz', nullable: true })
  arrivalWindowUntil: Date | null

  @Column(() => SeatsDemand)
  seatsDemand: SeatsDemand

  @AddressColumn({ nullable: true })
  pickupAddress: Address | null

  @AssistanceTypeColumn()
  pickupAssistanceType: AssistanceType

  @Column({ type: 'int', nullable: true })
  pickupStopDuration: number | null

  @Column({ type: 'int', nullable: true })
  pickupActionDuration: number | null

  @Column({ type: 'varchar', nullable: true })
  pickupRemarksForDriver: string | null

  @Column({ type: 'varchar', nullable: true })
  pickupRemarksForPlanner: string | null

  @AddressColumn({ nullable: true })
  dropOffAddress: Address | null

  @AssistanceTypeColumn()
  dropOffAssistanceType: AssistanceType

  @Column({ type: 'int', nullable: true })
  dropOffStopDuration: number | null

  @Column({ type: 'int', nullable: true })
  dropOffActionDuration: number | null

  @Column({ type: 'varchar', nullable: true })
  dropOffRemarksForDriver: string | null

  @Column({ type: 'varchar', nullable: true })
  dropOffRemarksForPlanner: string | null

  @Column({ type: 'uuid' })
  orderRequestUuid: string

  @ManyToOne(() => OrderRequest)
  @JoinColumn({ name: 'order_request_uuid' })
  orderRequest?: Relation<OrderRequest>
}
