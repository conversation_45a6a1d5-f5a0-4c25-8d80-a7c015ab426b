import { randomUUID } from 'crypto'
import { Address } from '../../../utils/address/address.js'
import { TargetAction } from '../accepted-transport-order/enums/target-action.enum.js'
import { SeatsDemand } from '../seats-demand/seats-demand.js'
import { DayOfWeek } from '../../pricing/pricing-parameters/types/flexibility-factor/day-of-week.enum.js'
import { AssistanceType } from './assistance-type.enum.js'
import { RequestedTransportOrder } from './requested-transport-order.entity.js'
import { generateRequestedTransportOrderUuid, RequestedTransportOrderUuid } from './requested-transport-order.uuid.js'

export class RequestedTransportOrderEntityBuilder {
  private readonly requestedTransportOrder: RequestedTransportOrder

  constructor () {
    this.requestedTransportOrder = new RequestedTransportOrder()
    this.requestedTransportOrder.uuid = generateRequestedTransportOrderUuid()
    this.requestedTransportOrder.createdAt = new Date()
    this.requestedTransportOrder.updatedAt = new Date()
    this.requestedTransportOrder.date = null
    this.requestedTransportOrder.daysOfWeek = null
    this.requestedTransportOrder.planOnHolidays = null
    this.requestedTransportOrder.targetAction = TargetAction.PICKUP
    this.requestedTransportOrder.targetTime = null
    this.requestedTransportOrder.arrivalWindowFrom = null
    this.requestedTransportOrder.arrivalWindowUntil = null
    this.requestedTransportOrder.seatsDemand = new SeatsDemand(1, 0, 0, 0)
    this.requestedTransportOrder.pickupAddress = null
    this.requestedTransportOrder.pickupAssistanceType = AssistanceType.NONE
    this.requestedTransportOrder.pickupStopDuration = null
    this.requestedTransportOrder.pickupActionDuration = null
    this.requestedTransportOrder.pickupRemarksForDriver = null
    this.requestedTransportOrder.pickupRemarksForPlanner = null
    this.requestedTransportOrder.dropOffAddress = null
    this.requestedTransportOrder.dropOffAssistanceType = AssistanceType.NONE
    this.requestedTransportOrder.dropOffStopDuration = null
    this.requestedTransportOrder.dropOffActionDuration = null
    this.requestedTransportOrder.dropOffRemarksForDriver = null
    this.requestedTransportOrder.dropOffRemarksForPlanner = null
    this.requestedTransportOrder.orderRequestUuid = randomUUID()
  }

  withUuid (uuid: RequestedTransportOrderUuid): this {
    this.requestedTransportOrder.uuid = uuid
    return this
  }

  withDate (date: string | null): this {
    this.requestedTransportOrder.date = date
    return this
  }

  withDaysOfWeek (daysOfWeek: DayOfWeek[] | null): this {
    this.requestedTransportOrder.daysOfWeek = daysOfWeek
    return this
  }

  withPlanOnHolidays (planOnHolidays: boolean | null): this {
    this.requestedTransportOrder.planOnHolidays = planOnHolidays
    return this
  }

  withTargetAction (targetAction: TargetAction): this {
    this.requestedTransportOrder.targetAction = targetAction
    return this
  }

  withTargetTime (targetTime: Date | null): this {
    this.requestedTransportOrder.targetTime = targetTime
    return this
  }

  withArrivalWindowFrom (arrivalWindowFrom: Date | null): this {
    this.requestedTransportOrder.arrivalWindowFrom = arrivalWindowFrom
    return this
  }

  withArrivalWindowUntil (arrivalWindowUntil: Date | null): this {
    this.requestedTransportOrder.arrivalWindowUntil = arrivalWindowUntil
    return this
  }

  withSeatsDemand (seatsDemand: SeatsDemand): this {
    this.requestedTransportOrder.seatsDemand = seatsDemand
    return this
  }

  withPickupAddress (pickupAddress: Address | null): this {
    this.requestedTransportOrder.pickupAddress = pickupAddress
    return this
  }

  withPickupAssistanceType (pickupAssistanceType: AssistanceType): this {
    this.requestedTransportOrder.pickupAssistanceType = pickupAssistanceType
    return this
  }

  withPickupStopDuration (pickupStopDuration: number | null): this {
    this.requestedTransportOrder.pickupStopDuration = pickupStopDuration
    return this
  }

  withPickupActionDuration (pickupActionDuration: number | null): this {
    this.requestedTransportOrder.pickupActionDuration = pickupActionDuration
    return this
  }

  withPickupRemarksForDriver (pickupRemarksForDriver: string | null): this {
    this.requestedTransportOrder.pickupRemarksForDriver = pickupRemarksForDriver
    return this
  }

  withPickupRemarksForPlanner (pickupRemarksForPlanner: string | null): this {
    this.requestedTransportOrder.pickupRemarksForPlanner = pickupRemarksForPlanner
    return this
  }

  withDropOffAddress (dropOffAddress: Address | null): this {
    this.requestedTransportOrder.dropOffAddress = dropOffAddress
    return this
  }

  withDropOffAssistanceType (dropOffAssistanceType: AssistanceType): this {
    this.requestedTransportOrder.dropOffAssistanceType = dropOffAssistanceType
    return this
  }

  withDropOffStopDuration (dropOffStopDuration: number | null): this {
    this.requestedTransportOrder.dropOffStopDuration = dropOffStopDuration
    return this
  }

  withDropOffActionDuration (dropOffActionDuration: number | null): this {
    this.requestedTransportOrder.dropOffActionDuration = dropOffActionDuration
    return this
  }

  withDropOffRemarksForDriver (dropOffRemarksForDriver: string | null): this {
    this.requestedTransportOrder.dropOffRemarksForDriver = dropOffRemarksForDriver
    return this
  }

  withDropOffRemarksForPlanner (dropOffRemarksForPlanner: string | null): this {
    this.requestedTransportOrder.dropOffRemarksForPlanner = dropOffRemarksForPlanner
    return this
  }

  withOrderRequestUuid (orderRequestUuid: string): this {
    this.requestedTransportOrder.orderRequestUuid = orderRequestUuid
    return this
  }

  build (): RequestedTransportOrder {
    return this.requestedTransportOrder
  }
}
