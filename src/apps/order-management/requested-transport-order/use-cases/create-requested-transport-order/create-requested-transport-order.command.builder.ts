import { AssistanceType } from '../../assistance-type.enum.js'
import { DayOfWeek } from '../../../../pricing/pricing-parameters/types/flexibility-factor/day-of-week.enum.js'
import { TargetAction } from '../../../accepted-transport-order/enums/target-action.enum.js'
import { AddressCommand } from '../../../../../utils/address/address-command.js'
import { SeatsDemand } from '../../../seats-demand/seats-demand.js'
import { CreateRequestedTransportOrderCommand } from './create-requested-transport-order.command.js'

export class CreateRequestedTransportOrderCommandBuilder {
  private readonly command: CreateRequestedTransportOrderCommand

  constructor () {
    this.command = new CreateRequestedTransportOrderCommand()
    this.command.date = null
    this.command.daysOfWeek = null
    this.command.planOnHolidays = null
    this.command.targetAction = TargetAction.PICKUP
    this.command.targetTime = null
    this.command.arrivalWindowFrom = null
    this.command.arrivalWindowUntil = null
    this.command.seatsDemand = {
      normalSeats: 1,
      wheelChairSeats: 0,
      electricWheelChairSeats: 0,
      childSeats: 0
    }
    this.command.pickupAddress = null
    this.command.pickupAssistanceType = AssistanceType.NONE
    this.command.pickupStopDuration = null
    this.command.pickupActionDuration = null
    this.command.pickupRemarksForDriver = null
    this.command.pickupRemarksForPlanner = null
    this.command.dropOffAddress = null
    this.command.dropOffAssistanceType = AssistanceType.NONE
    this.command.dropOffStopDuration = null
    this.command.dropOffActionDuration = null
    this.command.dropOffRemarksForDriver = null
    this.command.dropOffRemarksForPlanner = null
  }

  withDate (date: string | null): this {
    this.command.date = date
    return this
  }

  withDaysOfWeek (daysOfWeek: DayOfWeek[] | null): this {
    this.command.daysOfWeek = daysOfWeek
    return this
  }

  withPlanOnHolidays (planOnHolidays: boolean | null): this {
    this.command.planOnHolidays = planOnHolidays
    return this
  }

  withTargetAction (targetAction: TargetAction): this {
    this.command.targetAction = targetAction
    return this
  }

  withTargetTime (targetTime: string | null): this {
    this.command.targetTime = targetTime
    return this
  }

  withArrivalWindowFrom (arrivalWindowFrom: string | null): this {
    this.command.arrivalWindowFrom = arrivalWindowFrom
    return this
  }

  withArrivalWindowUntil (arrivalWindowUntil: string | null): this {
    this.command.arrivalWindowUntil = arrivalWindowUntil
    return this
  }

  withSeatsDemand (seatsDemand: SeatsDemand): this {
    this.command.seatsDemand = seatsDemand
    return this
  }

  withPickupAddress (pickupAddress: AddressCommand | null): this {
    this.command.pickupAddress = pickupAddress
    return this
  }

  withPickupAssistanceType (pickupAssistanceType: AssistanceType): this {
    this.command.pickupAssistanceType = pickupAssistanceType
    return this
  }

  withPickupStopDuration (pickupStopDuration: number | null): this {
    this.command.pickupStopDuration = pickupStopDuration
    return this
  }

  withPickupActionDuration (pickupActionDuration: number | null): this {
    this.command.pickupActionDuration = pickupActionDuration
    return this
  }

  withPickupRemarksForDriver (pickupRemarksForDriver: string | null): this {
    this.command.pickupRemarksForDriver = pickupRemarksForDriver
    return this
  }

  withPickupRemarksForPlanner (pickupRemarksForPlanner: string | null): this {
    this.command.pickupRemarksForPlanner = pickupRemarksForPlanner
    return this
  }

  withDropOffAddress (dropOffAddress: AddressCommand | null): this {
    this.command.dropOffAddress = dropOffAddress
    return this
  }

  withDropOffAssistanceType (dropOffAssistanceType: AssistanceType): this {
    this.command.dropOffAssistanceType = dropOffAssistanceType
    return this
  }

  withDropOffStopDuration (dropOffStopDuration: number | null): this {
    this.command.dropOffStopDuration = dropOffStopDuration
    return this
  }

  withDropOffActionDuration (dropOffActionDuration: number | null): this {
    this.command.dropOffActionDuration = dropOffActionDuration
    return this
  }

  withDropOffRemarksForDriver (dropOffRemarksForDriver: string | null): this {
    this.command.dropOffRemarksForDriver = dropOffRemarksForDriver
    return this
  }

  withDropOffRemarksForPlanner (dropOffRemarksForPlanner: string | null): this {
    this.command.dropOffRemarksForPlanner = dropOffRemarksForPlanner
    return this
  }

  build (): CreateRequestedTransportOrderCommand {
    return this.command
  }
}
