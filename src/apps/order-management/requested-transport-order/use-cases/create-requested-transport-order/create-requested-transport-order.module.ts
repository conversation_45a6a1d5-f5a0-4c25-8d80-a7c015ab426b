import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { RequestedTransportOrder } from '../../requested-transport-order.entity.js'
import { OrderRequest } from '../../../order-request/entities/order-request.entity.js'
import { CreateRequestedTransportOrderController } from './create-requested-transport-order.controller.js'
import { CreateRequestedTransportOrderUseCase } from './create-requested-transport-order.use-case.js'
import { CreateRequestedTransportOrderRepository } from './create-requested-transport-order.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([RequestedTransportOrder, OrderRequest]),
    DomainEventEmitterModule
  ],
  controllers: [CreateRequestedTransportOrderController],
  providers: [CreateRequestedTransportOrderUseCase, CreateRequestedTransportOrderRepository],
  exports: [CreateRequestedTransportOrderUseCase]
})
export class CreateRequestedTransportOrderModule {}
