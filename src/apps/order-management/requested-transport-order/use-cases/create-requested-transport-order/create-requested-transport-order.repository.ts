import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { RequestedTransportOrder } from '../../requested-transport-order.entity.js'
import { OrderRequest } from '../../../order-request/entities/order-request.entity.js'

@Injectable()
export class CreateRequestedTransportOrderRepository {
  constructor (
    @InjectRepository(RequestedTransportOrder)
    private readonly requestedTransportOrderRepository: Repository<RequestedTransportOrder>,
    @InjectRepository(OrderRequest)
    private readonly orderRequestRepository: Repository<OrderRequest>
  ) {}

  async findOrderRequest (orderRequestUuid: string): Promise<OrderRequest | null> {
    return await this.orderRequestRepository.findOne({
      where: { uuid: orderRequestUuid }
    })
  }

  async insert (requestedTransportOrder: RequestedTransportOrder): Promise<void> {
    await this.requestedTransportOrderRepository.insert(requestedTransportOrder)
  }
}
