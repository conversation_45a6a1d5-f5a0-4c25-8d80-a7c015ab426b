import { ApiProperty } from '@nestjs/swagger'
import { RequestedTransportOrder } from '../../requested-transport-order.entity.js'
import { AddressResponse } from '../../../../../utils/address/address-response.js'
import { AssistanceType, AssistanceTypeApiProperty } from '../../assistance-type.enum.js'
import { DayOfWeek, DayOfWeekApiProperty } from '../../../../pricing/pricing-parameters/types/flexibility-factor/day-of-week.enum.js'
import { TargetAction, TargetActionApiProperty } from '../../../accepted-transport-order/enums/target-action.enum.js'
import { SeatsDemandResponse } from '../../../seats-demand/seats-demand.response.js'

export class CreateRequestedTransportOrderResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, format: 'date', nullable: true })
  date: string | null

  @DayOfWeekApiProperty({ type: [String], nullable: true })
  daysOfWeek: DayOfWeek[] | null

  @ApiProperty({ type: Boolean, nullable: true })
  planOnHolidays: boolean | null

  @TargetActionApiProperty()
  targetAction: TargetAction

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  targetTime: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  arrivalWindowFrom: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  arrivalWindowUntil: string | null

  @ApiProperty({ type: SeatsDemandResponse })
  seatsDemand: SeatsDemandResponse

  @ApiProperty({ type: AddressResponse, nullable: true })
  pickupAddress: AddressResponse | null

  @AssistanceTypeApiProperty()
  pickupAssistanceType: AssistanceType

  @ApiProperty({ type: Number, nullable: true })
  pickupStopDuration: number | null

  @ApiProperty({ type: Number, nullable: true })
  pickupActionDuration: number | null

  @ApiProperty({ type: String, nullable: true })
  pickupRemarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  pickupRemarksForPlanner: string | null

  @ApiProperty({ type: AddressResponse, nullable: true })
  dropOffAddress: AddressResponse | null

  @AssistanceTypeApiProperty()
  dropOffAssistanceType: AssistanceType

  @ApiProperty({ type: Number, nullable: true })
  dropOffStopDuration: number | null

  @ApiProperty({ type: Number, nullable: true })
  dropOffActionDuration: number | null

  @ApiProperty({ type: String, nullable: true })
  dropOffRemarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  dropOffRemarksForPlanner: string | null

  @ApiProperty({ type: String, format: 'uuid' })
  orderRequestUuid: string

  constructor (requestedTransportOrder: RequestedTransportOrder) {
    this.uuid = requestedTransportOrder.uuid
    this.createdAt = requestedTransportOrder.createdAt.toISOString()
    this.updatedAt = requestedTransportOrder.updatedAt.toISOString()
    this.date = requestedTransportOrder.date
    this.daysOfWeek = requestedTransportOrder.daysOfWeek
    this.planOnHolidays = requestedTransportOrder.planOnHolidays
    this.targetAction = requestedTransportOrder.targetAction
    this.targetTime = requestedTransportOrder.targetTime?.toISOString() ?? null
    this.arrivalWindowFrom = requestedTransportOrder.arrivalWindowFrom?.toISOString() ?? null
    this.arrivalWindowUntil = requestedTransportOrder.arrivalWindowUntil?.toISOString() ?? null
    this.seatsDemand = new SeatsDemandResponse(requestedTransportOrder.seatsDemand)
    this.pickupAddress = requestedTransportOrder.pickupAddress
      ? new AddressResponse(requestedTransportOrder.pickupAddress)
      : null
    this.pickupAssistanceType = requestedTransportOrder.pickupAssistanceType
    this.pickupStopDuration = requestedTransportOrder.pickupStopDuration
    this.pickupActionDuration = requestedTransportOrder.pickupActionDuration
    this.pickupRemarksForDriver = requestedTransportOrder.pickupRemarksForDriver
    this.pickupRemarksForPlanner = requestedTransportOrder.pickupRemarksForPlanner
    this.dropOffAddress = requestedTransportOrder.dropOffAddress
      ? new AddressResponse(requestedTransportOrder.dropOffAddress)
      : null
    this.dropOffAssistanceType = requestedTransportOrder.dropOffAssistanceType
    this.dropOffStopDuration = requestedTransportOrder.dropOffStopDuration
    this.dropOffActionDuration = requestedTransportOrder.dropOffActionDuration
    this.dropOffRemarksForDriver = requestedTransportOrder.dropOffRemarksForDriver
    this.dropOffRemarksForPlanner = requestedTransportOrder.dropOffRemarksForPlanner
    this.orderRequestUuid = requestedTransportOrder.orderRequestUuid
  }
}
