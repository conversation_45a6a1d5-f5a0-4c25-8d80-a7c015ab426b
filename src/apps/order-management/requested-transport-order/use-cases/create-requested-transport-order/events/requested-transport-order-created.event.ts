import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../../modules/domain-events/register-domain-event.decorator.js'

@OneOfMeta(DomainEventLog, DomainEventType.REQUESTED_TRANSPORT_ORDER_CREATED)
export class RequestedTransportOrderCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly requestedTransportOrderUuid: string

  @ApiProperty({ format: 'uuid' })
  readonly orderRequestUuid: string

  constructor (requestedTransportOrderUuid: string, orderRequestUuid: string) {
    this.requestedTransportOrderUuid = requestedTransportOrderUuid
    this.orderRequestUuid = orderRequestUuid
  }
}

@RegisterDomainEvent(DomainEventType.REQUESTED_TRANSPORT_ORDER_CREATED, 1)
export class RequestedTransportOrderCreatedEvent
  extends DomainEvent<RequestedTransportOrderCreatedEventContent> {
  constructor (requestedTransportOrderUuid: string, orderRequestUuid: string) {
    super({ content: new RequestedTransportOrderCreatedEventContent(
      requestedTransportOrderUuid,
      orderRequestUuid) })
  }
}
