import { ApiProperty } from '@nestjs/swagger'
import { ArrayUnique, IsArray, IsBoolean, IsDateString, IsEnum, IsInt, IsNotEmpty, IsString, Min, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { IsNullable } from '@wisemen/validators'
import { IsDateWithoutTimeString } from '../../../../../utils/validators/is-date-without-time-string.validator.js'
import { AddressCommand } from '../../../../../utils/address/address-command.js'
import { IsAddress } from '../../../../../utils/address/is-address.validator.js'
import { AssistanceType, AssistanceTypeApiProperty } from '../../assistance-type.enum.js'
import { DayOfWeek, DayOfWeekApiProperty } from '../../../../pricing/pricing-parameters/types/flexibility-factor/day-of-week.enum.js'
import { TargetAction, TargetActionApiProperty } from '../../../accepted-transport-order/enums/target-action.enum.js'
import { SeatsDemandCommand } from '../../../seats-demand/create-seat-demand.command.js'

export class CreateRequestedTransportOrderCommand {
  @ApiProperty({ type: String, format: 'date', nullable: true })
  @IsNullable()
  @IsDateWithoutTimeString()
  date: string | null

  @DayOfWeekApiProperty({ type: [String], nullable: true })
  @IsNullable()
  @IsArray()
  @ArrayUnique()
  @IsEnum(DayOfWeek, { each: true })
  daysOfWeek: DayOfWeek[] | null

  @ApiProperty({ type: Boolean, nullable: true })
  @IsNullable()
  @IsBoolean()
  planOnHolidays: boolean | null

  @TargetActionApiProperty()
  @IsEnum(TargetAction)
  @IsNotEmpty()
  targetAction: TargetAction

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  @IsNullable()
  @IsDateString()
  targetTime: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  @IsNullable()
  @IsDateString()
  arrivalWindowFrom: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  @IsNullable()
  @IsDateString()
  arrivalWindowUntil: string | null

  @ApiProperty({ type: SeatsDemandCommand })
  @ValidateNested()
  @Type(() => SeatsDemandCommand)
  seatsDemand: SeatsDemandCommand

  @ApiProperty({ type: AddressCommand, nullable: true })
  @IsNullable()
  @IsAddress({ })
  pickupAddress: AddressCommand | null

  @AssistanceTypeApiProperty()
  @IsEnum(AssistanceType)
  @IsNotEmpty()
  pickupAssistanceType: AssistanceType

  @ApiProperty({ type: Number, nullable: true })
  @IsNullable()
  @IsInt()
  @Min(0)
  pickupStopDuration: number | null

  @ApiProperty({ type: Number, nullable: true })
  @IsNullable()
  @IsInt()
  @Min(0)
  pickupActionDuration: number | null

  @ApiProperty({ type: String, nullable: true })
  @IsNullable()
  @IsString()
  pickupRemarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  @IsNullable()
  @IsString()
  pickupRemarksForPlanner: string | null

  @ApiProperty({ type: AddressCommand, nullable: true })
  @IsNullable()
  @IsAddress({ })
  dropOffAddress: AddressCommand | null

  @AssistanceTypeApiProperty()
  @IsEnum(AssistanceType)
  @IsNotEmpty()
  dropOffAssistanceType: AssistanceType

  @ApiProperty({ type: Number, nullable: true })
  @IsNullable()
  @IsInt()
  @Min(0)
  dropOffStopDuration: number | null

  @ApiProperty({ type: Number, nullable: true })
  @IsNullable()
  @IsInt()
  @Min(0)
  dropOffActionDuration: number | null

  @ApiProperty({ type: String, nullable: true })
  @IsNullable()
  @IsString()
  dropOffRemarksForDriver: string | null

  @ApiProperty({ type: String, nullable: true })
  @IsNullable()
  @IsString()
  dropOffRemarksForPlanner: string | null
}
