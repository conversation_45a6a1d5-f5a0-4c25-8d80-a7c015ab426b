import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { OrderRequest } from '../../../../order-request/entities/order-request.entity.js'
import { CreateRequestedTransportOrderCommandBuilder } from '../create-requested-transport-order.command.builder.js'
import { AssistanceType } from '../../../assistance-type.enum.js'
import { AddressCommandBuilder } from '../../../../../../utils/address/address-command.builder.js'
import { setupOrderRequestTestContext } from '../../../../order-request/use-cases/update-order-request/tests/update-order-request-use-case.test-context.js'

describe('Create requested transport order e2e test', () => {
  let setup: EndToEndTestSetup
  let adminUser: TestUser
  let orderRequest: OrderRequest

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    adminUser = await setup.authContext.getAdminUser()

    orderRequest = (await setupOrderRequestTestContext(setup.dataSource.manager)).orderRequest
  })

  after(async () => {
    await setup.teardown()
  })

  it('creates a new requested transport order', async () => {
    const command = new CreateRequestedTransportOrderCommandBuilder()
      .withPickupAssistanceType(AssistanceType.PICKUP)
      .withDropOffAssistanceType(AssistanceType.DROP_OFF)
      .withPickupAddress(new AddressCommandBuilder()
        .withPlaceId('pickup-place-id')
        .build())
      .withDropOffAddress(new AddressCommandBuilder()
        .withPlaceId('dropoff-place-id')
        .build())
      .withPickupStopDuration(10)
      .withPickupActionDuration(5)
      .withDropOffStopDuration(15)
      .withDropOffActionDuration(8)
      .build()

    const response = await request(setup.httpServer)
      .post(`/order-requests/${orderRequest.uuid}/requested-transport-orders`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
    expect(response.body.pickupAssistanceType).toBe(AssistanceType.PICKUP)
    expect(response.body.dropOffAssistanceType).toBe(AssistanceType.DROP_OFF)
    expect(response.body.pickupAddress.placeId).toBe('pickup-place-id')
    expect(response.body.dropOffAddress.placeId).toBe('dropoff-place-id')
    expect(response.body.pickupStopDuration).toBe(10)
    expect(response.body.pickupActionDuration).toBe(5)
    expect(response.body.dropOffStopDuration).toBe(15)
    expect(response.body.dropOffActionDuration).toBe(8)
  })
})
