import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { CreateRequestedTransportOrderUseCase } from '../create-requested-transport-order.use-case.js'
import { CreateRequestedTransportOrderRepository } from '../create-requested-transport-order.repository.js'
import { CreateRequestedTransportOrderCommandBuilder } from '../create-requested-transport-order.command.builder.js'
import { RequestedTransportOrderCreatedEvent } from '../events/requested-transport-order-created.event.js'
import { OrderRequestEntityBuilder } from '../../../../order-request/entities/order-request.entity.builder.js'
import { OrderRequestNotFoundError } from '../../../../order-request/errors/order-request-not-found.error.js'

describe('CreateRequestedTransportOrderUseCase - Unit Tests', () => {
  before(() => TestBench.setupUnitTest())

  it('Emits a requested transport order created event when creating a new requested transport order', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const repository = createStubInstance(CreateRequestedTransportOrderRepository)

    const orderRequest = new OrderRequestEntityBuilder().build()
    repository.findOrderRequest.resolves(orderRequest)

    const useCase = new CreateRequestedTransportOrderUseCase(
      stubDataSource(),
      repository,
      eventEmitter
    )

    const command = new CreateRequestedTransportOrderCommandBuilder()
      .build()

    const response = await useCase.execute(orderRequest.uuid, command)

    expect(eventEmitter).toHaveEmitted(
      new RequestedTransportOrderCreatedEvent(response.uuid, response.orderRequestUuid)
    )
  })

  it('Throws an error when order request does not exist', async () => {
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const repository = createStubInstance(CreateRequestedTransportOrderRepository)

    repository.findOrderRequest.resolves(null)

    const useCase = new CreateRequestedTransportOrderUseCase(
      stubDataSource(),
      repository,
      eventEmitter
    )

    const command = new CreateRequestedTransportOrderCommandBuilder().build()

    await expect(async () => await useCase.execute(randomUUID(), command))
      .rejects.toThrow(OrderRequestNotFoundError)
  })
})
