import { Injectable } from '@nestjs/common'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { RequestedTransportOrderEntityBuilder } from '../../requested-transport-order.entity.builder.js'
import { SeatsDemand } from '../../../seats-demand/seats-demand.js'
import { OrderRequestNotFoundError } from '../../../order-request/errors/order-request-not-found.error.js'
import { CreateRequestedTransportOrderCommand } from './create-requested-transport-order.command.js'
import { CreateRequestedTransportOrderRepository } from './create-requested-transport-order.repository.js'
import { CreateRequestedTransportOrderResponse } from './create-requested-transport-order.response.js'
import { RequestedTransportOrderCreatedEvent } from './events/requested-transport-order-created.event.js'

@Injectable()
export class CreateRequestedTransportOrderUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly repository: CreateRequestedTransportOrderRepository,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    orderRequestUuid: string,
    command: CreateRequestedTransportOrderCommand
  ): Promise<CreateRequestedTransportOrderResponse> {
    const orderRequest = await this.repository.findOrderRequest(orderRequestUuid)
    if (!orderRequest) {
      throw new OrderRequestNotFoundError(orderRequestUuid)
    }

    // Validate recurring vs non-recurring logic
    if (orderRequest.isRecurring && command.date !== null) {
      throw new Error('Date must be null for recurring order requests')
    } else if (command.daysOfWeek !== null || command.planOnHolidays !== null) {
      throw new Error('DaysOfWeek and planOnHolidays must be null for non-recurring order requests')
    }

    const seatsDemand = new SeatsDemand(
      command.seatsDemand.normalSeats,
      command.seatsDemand.wheelChairSeats,
      command.seatsDemand.electricWheelChairSeats,
      command.seatsDemand.childSeats
    )

    const requestedTransportOrder = new RequestedTransportOrderEntityBuilder()
      .withOrderRequestUuid(orderRequestUuid)
      .withDate(command.date)
      .withDaysOfWeek(command.daysOfWeek)
      .withPlanOnHolidays(command.planOnHolidays)
      .withTargetAction(command.targetAction)
      .withTargetTime(command.targetTime !== null
        ? new Date(command.targetTime)
        : null)
      .withArrivalWindowFrom(command.arrivalWindowFrom !== null
        ? new Date(command.arrivalWindowFrom)
        : null)
      .withArrivalWindowUntil(command.arrivalWindowUntil !== null
        ? new Date(command.arrivalWindowUntil)
        : null)
      .withSeatsDemand(seatsDemand)
      .withPickupAddress(command.pickupAddress?.parse() ?? null)
      .withPickupAssistanceType(command.pickupAssistanceType)
      .withPickupStopDuration(command.pickupStopDuration)
      .withPickupActionDuration(command.pickupActionDuration)
      .withPickupRemarksForDriver(command.pickupRemarksForDriver)
      .withPickupRemarksForPlanner(command.pickupRemarksForPlanner)
      .withDropOffAddress(command.dropOffAddress?.parse() ?? null)
      .withDropOffAssistanceType(command.dropOffAssistanceType)
      .withDropOffStopDuration(command.dropOffStopDuration)
      .withDropOffActionDuration(command.dropOffActionDuration)
      .withDropOffRemarksForDriver(command.dropOffRemarksForDriver)
      .withDropOffRemarksForPlanner(command.dropOffRemarksForPlanner)
      .build()

    await transaction(this.dataSource, async () => {
      await this.repository.insert(requestedTransportOrder)
      await this.eventEmitter.emitOne(
        new RequestedTransportOrderCreatedEvent(
          requestedTransportOrder.uuid,
          requestedTransportOrder.orderRequestUuid
        )
      )
    })

    return new CreateRequestedTransportOrderResponse(requestedTransportOrder)
  }
}
