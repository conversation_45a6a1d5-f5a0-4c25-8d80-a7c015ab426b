import { Body, Controller, Post } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { Permissions } from '../../../../../modules/permission/permission.decorator.js'
import { CreateRequestedTransportOrderCommand } from './create-requested-transport-order.command.js'
import { CreateRequestedTransportOrderResponse } from './create-requested-transport-order.response.js'
import { CreateRequestedTransportOrderUseCase } from './create-requested-transport-order.use-case.js'

@ApiTags('Order Request')
@ApiOAuth2([])
@Controller('/order-requests/:orderRequestUuid/requested-transport-orders')
export class CreateRequestedTransportOrderController {
  constructor (
    private readonly createRequestedTransportOrderUseCase: CreateRequestedTransportOrderUseCase
  ) {}

  @Post()
  @Permissions(Permission.ORDER_REQUEST_CREATE)
  @ApiCreatedResponse({ type: CreateRequestedTransportOrderResponse })
  async createRequestedTransportOrder (
    @UuidParam('orderRequestUuid') orderRequestUuid: string,
    @Body() command: CreateRequestedTransportOrderCommand
  ): Promise<CreateRequestedTransportOrderResponse> {
    return this.createRequestedTransportOrderUseCase.execute(orderRequestUuid, command)
  }
}
