import { ApiProperty } from '@nestjs/swagger'
import { SeatsDemand } from './seats-demand.js'

export class SeatsDemandResponse {
  @ApiProperty({ type: 'integer', format: 'int32' })
  normalSeats: number

  @ApiProperty({ type: 'integer', format: 'int32' })
  wheelChairSeats: number

  @ApiProperty({ type: 'integer', format: 'int32' })
  electricWheelChairSeats: number

  @ApiProperty({ type: 'integer', format: 'int32' })
  childSeats: number

  constructor (seatsDemand: SeatsDemand) {
    this.normalSeats = seatsDemand.normalSeats
    this.wheelChairSeats = seatsDemand.wheelChairSeats
    this.electricWheelChairSeats = seatsDemand.electricWheelChairSeats
    this.childSeats = seatsDemand.childSeats
  }
}
