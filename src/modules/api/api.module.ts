import { MiddlewareConsumer, Module } from '@nestjs/common'
import { AppModule } from '../../app.module.js'
import { AuthModule } from '../auth/auth.module.js'
import { SwaggerModule } from '../swagger/swagger.module.js'
import { StatusModule } from '../status/status.module.js'
import { UserModule } from '../../app/users/user.module.js'
import { RoleModule } from '../../app/roles/role.module.js'
import { PermissionModule } from '../permission/permission.module.js'
import { FileModule } from '../files/file.module.js'
import { LocalizationModule } from '../localization/modules/localization.module.js'
import { UiPreferencesModule } from '../../app/ui-preferences/ui-preferences.module.js'
import { AuthMiddleware } from '../auth/middleware/auth.middleware.js'
import { DomainEventLogModule } from '../domain-event-log/domain-event-log.module.js'
import { OneSignalModule } from '../one-signal/one-signal.module.js'
import { GlobalSearchModule } from '../global-search/global-search.module.js'
import { NotificationModule } from '../notification/notification.module.js'
import { JobsApiModule } from '../jobs/jobs.api-module.js'
import { AcceptedTransportOrderModule } from '../../apps/order-management/accepted-transport-order/accepted-transport-order.module.js'
import { CareUserModule } from '../../apps/order-management/care-user/care-user.module.js'
import { CareUserAddressModule } from '../../apps/order-management/care-user-address/care-user-address.module.js'
import { ContractTypeModule } from '../../apps/order-management/contract-type/contract-type.module.js'
import { ContractModule } from '../../apps/order-management/contract/contract.module.js'
import { MaxTimeInVehicleFormulaModule } from '../../apps/order-management/max-time-in-vehicle-formula/max-time-in-vehicle-formula.module.js'
import { OrganizationModule } from '../../apps/order-management/organization/organization.module.js'
import { SimulationModule } from '../../apps/planning/simulation/simulation.module.js'
import { AbsenceModule } from '../../apps/resource-management/absence/absence.module.js'
import { BranchModule } from '../../apps/resource-management/branch/branch.module.js'
import { DriverModule } from '../../apps/resource-management/driver/driver.module.js'
import { ShiftModule } from '../../apps/resource-management/shift/shift.module.js'
import { DatabaseSeederModule } from '../database-seeder/database-seeder.module.js'
import { LocationModule } from '../location/location.module.js'
import { ClientModule } from '../../apps/order-management/client/client-module.js'
import { OrderRequestModule } from '../../apps/order-management/order-request/order-request.module.js'
import { RequestedTransportOrderModule } from '../../apps/order-management/requested-transport-order/requested-transport-order.module.js'
import { VirtualSegmentModule } from '../../apps/order-management/virtual-segment/virtual-segment.module.js'
import { DriverAvailabilitiesModule } from '../../apps/resource-management/driver-availabilities/driver-availabilities.module.js'
import { PricingFormulaModule } from '../../apps/pricing/pricing-formula/pricing-formula.module.js'
import { PricingParametersModule } from '../../apps/pricing/pricing-parameters/pricing-parameters.module.js'
import { HolidayModule } from '../holidays/holiday.module.js'
import { VehicleModule } from '../../apps/resource-management/vehicle/vehicle.module.js'
import { DriverUnavailabilityModule } from '../../apps/resource-management/driver-unavailability/driver-unavailability.module.js'
import { VehicleUnavailabilityModule } from '../../apps/resource-management/vehicle-unavailability/vehicle-unavailability.module.js'

@Module({
  imports: [
    AppModule.forRoot(),
    AuthModule,
    SwaggerModule,
    StatusModule,
    UserModule,
    RoleModule,
    PermissionModule,
    FileModule,
    LocalizationModule,
    UiPreferencesModule,
    OneSignalModule,
    DomainEventLogModule,
    GlobalSearchModule,
    NotificationModule,
    JobsApiModule,
    DatabaseSeederModule,
    DriverModule,
    AcceptedTransportOrderModule,
    ShiftModule,
    ContractTypeModule,
    ContractModule,
    OrganizationModule,
    SimulationModule,
    AbsenceModule,
    LocationModule,
    BranchModule,
    CareUserModule,
    CareUserAddressModule,
    MaxTimeInVehicleFormulaModule,
    ClientModule,
    OrderRequestModule,
    RequestedTransportOrderModule,
    VirtualSegmentModule,
    DriverAvailabilitiesModule,
    PricingParametersModule,
    PricingFormulaModule,
    HolidayModule,
    VehicleModule,
    DriverUnavailabilityModule,
    VehicleUnavailabilityModule
  ]
})
export class ApiModule {
  configure (consumer: MiddlewareConsumer): void {
    consumer
      .apply(AuthMiddleware)
      .exclude('auth/token')
      .forRoutes('{*all}')
  }
}
